import React, { useMemo } from 'react';
import {
  Box,
  Typo<PERSON>,
  TextField,
  InputAdornment,
  List,
  ListItem,
  Divider,
  Chip,
  Alert,
  Stack,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  OutlinedInput,
  SelectChangeEvent,
} from '@mui/material';
import { Search, History, Star, LocalOffer } from '@mui/icons-material';
import { useAppSelector, useAppDispatch } from '@/store';
import { setSearchQuery, setSelectedEntry, setSelectedTags } from '@/store/slices/clipboardSlice';
import { useKeyboardShortcuts } from '@/hooks/useKeyboardShortcuts';
import ClipboardEntry from './ClipboardEntry';

interface ClipboardHistoryListProps {
  showFavoritesOnly?: boolean;
}

const ClipboardHistoryList: React.FC<ClipboardHistoryListProps> = ({
  showFavoritesOnly = false,
}) => {
  const dispatch = useAppDispatch();
  const { history, favorites, searchQuery, selectedTags } = useAppSelector((state) => state.clipboard);
  const { selectedEntryId } = useKeyboardShortcuts();

  const entries = showFavoritesOnly ? favorites : history;

  // Get all unique tags from entries
  const allTags = useMemo(() => {
    const tagSet = new Set<string>();
    entries.forEach(entry => {
      entry.tags.forEach(tag => tagSet.add(tag));
    });
    return Array.from(tagSet).sort();
  }, [entries]);

  // Filter entries based on search query and selected tags
  const filteredEntries = useMemo(() => {
    let filtered = entries;

    // Filter by selected tags
    if (selectedTags.length > 0) {
      filtered = filtered.filter(entry =>
        selectedTags.some(tag => entry.tags.includes(tag))
      );
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(entry =>
        entry.content.toLowerCase().includes(query) ||
        entry.name?.toLowerCase().includes(query) ||
        entry.tags.some(tag => tag.toLowerCase().includes(query)) ||
        entry.metadata?.source?.toLowerCase().includes(query)
      );
    }

    return filtered;
  }, [entries, searchQuery, selectedTags]);

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setSearchQuery(event.target.value));
  };

  const handleEntryClick = (entryId: string) => {
    dispatch(setSelectedEntry(entryId));
  };

  const handleEntryCopy = async (content: string) => {
    try {
      await navigator.clipboard.writeText(content);
      // Could add a toast notification here
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  const handleTagFilterChange = (event: SelectChangeEvent<string[]>) => {
    const value = event.target.value;
    dispatch(setSelectedTags(typeof value === 'string' ? value.split(',') : value));
  };

  if (entries.length === 0) {
    return (
      <Box
        display="flex"
        flexDirection="column"
        alignItems="center"
        justifyContent="center"
        height="100%"
        p={4}
      >
        {showFavoritesOnly ? (
          <>
            <Star sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              No Favorites Yet
            </Typography>
            <Typography variant="body2" color="text.secondary" textAlign="center">
              Star clipboard entries to add them to your favorites for quick access.
            </Typography>
          </>
        ) : (
          <>
            <History sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              No Clipboard History
            </Typography>
            <Typography variant="body2" color="text.secondary" textAlign="center">
              Copy something to get started. Your clipboard history will appear here.
            </Typography>
          </>
        )}
      </Box>
    );
  }

  return (
    <Box height="100%" display="flex" flexDirection="column">
      {/* Search Bar */}
      <Box p={2} pb={1}>
        <Stack spacing={2}>
          <TextField
            fullWidth
            size="small"
            placeholder={`Search ${showFavoritesOnly ? 'favorites' : 'clipboard history'}...`}
            value={searchQuery}
            onChange={handleSearchChange}
            data-testid="search-input"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search fontSize="small" />
                </InputAdornment>
              ),
            }}
            sx={{
              '& .MuiOutlinedInput-root': {
                backgroundColor: 'background.paper',
              },
            }}
          />
          
          {/* Tag Filter */}
          {allTags.length > 0 && (
            <FormControl size="small" fullWidth>
              <InputLabel id="tag-filter-label">Filter by Tags</InputLabel>
              <Select
                labelId="tag-filter-label"
                multiple
                value={selectedTags}
                onChange={handleTagFilterChange}
                input={<OutlinedInput label="Filter by Tags" />}
                renderValue={(selected) => (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {selected.map((value) => (
                      <Chip
                        key={value}
                        label={value}
                        size="small"
                        icon={<LocalOffer fontSize="small" />}
                        sx={{ height: 20 }}
                      />
                    ))}
                  </Box>
                )}
                data-testid="tag-filter-select"
              >
                {allTags.map((tag) => (
                  <MenuItem key={tag} value={tag}>
                    <Box display="flex" alignItems="center" gap={1}>
                      <LocalOffer fontSize="small" />
                      {tag}
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          )}
        </Stack>
      </Box>

      {/* Results Info */}
      <Box px={2} pb={1}>
        <Box display="flex" alignItems="center" gap={1}>
          <Chip
            label={`${filteredEntries.length} ${filteredEntries.length === 1 ? 'item' : 'items'}`}
            size="small"
            variant="outlined"
          />
          {searchQuery && (
            <Typography variant="caption" color="text.secondary">
              filtered by "{searchQuery}"
            </Typography>
          )}
        </Box>
      </Box>

      {/* Entry List */}
      <Box flex={1} overflow="auto" px={2}>
        {filteredEntries.length === 0 ? (
          <Alert severity="info" sx={{ mt: 2 }}>
            No entries match your search criteria.
          </Alert>
        ) : (
          <List disablePadding>
            {filteredEntries.map((entry, index) => (
              <ListItem key={entry.id} disablePadding sx={{ mb: 1 }}>
                <Box width="100%">
                  <ClipboardEntry
                    entry={entry}
                    isSelected={entry.id === selectedEntryId}
                    onClick={() => handleEntryClick(entry.id)}
                    onCopy={() => handleEntryCopy(entry.content)}
                  />
                  {index < filteredEntries.length - 1 && (
                    <Divider sx={{ my: 1, opacity: 0.3 }} />
                  )}
                </Box>
              </ListItem>
            ))}
          </List>
        )}
      </Box>
    </Box>
  );
};

export default ClipboardHistoryList;
