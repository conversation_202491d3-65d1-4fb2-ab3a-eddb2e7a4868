import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import ClipboardEntry from '@/components/ClipboardEntry';
import clipboardSlice from '@/store/slices/clipboardSlice';
import preferencesSlice from '@/store/slices/preferencesSlice';
import type { ClipboardEntry as ClipboardEntryType } from '@/types/clipboard';

// Mock Tauri API
const mockInvoke = vi.fn();
vi.mock('@tauri-apps/api/core', () => ({
  invoke: mockInvoke,
}));

const createMockStore = (initialState: any = {}) => {
  return configureStore({
    reducer: {
      clipboard: clipboardSlice,
      preferences: preferencesSlice,
    },
    preloadedState: {
      clipboard: {
        history: [],
        favorites: [],
        maxHistoryLength: 100,
        isMonitoring: false,
        searchQuery: '',
        selectedEntryId: null,
        selectedTags: [],
        ...initialState.clipboard,
      },
      preferences: {
        theme: 'dark',
        shortcuts: {
          toggleApp: 'CommandOrControl+Shift+V',
          clearHistory: 'CommandOrControl+Shift+Delete',
          toggleFavorite: 'CommandOrControl+D',
          copySelected: 'Enter',
          deleteSelected: 'Delete',
          search: 'CommandOrControl+F',
          navigateUp: 'ArrowUp',
          navigateDown: 'ArrowDown',
        },
        autoStart: false,
        showInSystemTray: true,
        maxHistoryLength: 100,
        ...initialState.preferences,
      },
    },
  });
};

const mockEntry: ClipboardEntryType = {
  id: 'test-id-1',
  content: 'Test clipboard content',
  type: 'text',
  timestamp: Date.now(),
  isFavorite: false,
  name: undefined,
  tags: [],
  preview: undefined,
  metadata: undefined,
};

const mockEntryWithNameAndTags: ClipboardEntryType = {
  id: 'test-id-2',
  content: 'Test clipboard content with name and tags',
  type: 'text',
  timestamp: Date.now(),
  isFavorite: true,
  name: 'Important Note',
  tags: ['work', 'important', 'meeting'],
  preview: undefined,
  metadata: undefined,
};

describe('ClipboardEntry Enhanced Features', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockInvoke.mockResolvedValue({});
  });

  describe('Name and Tags Display', () => {
    it('should display entry name when present', () => {
      const store = createMockStore();
      render(
        <Provider store={store}>
          <ClipboardEntry
            entry={mockEntryWithNameAndTags}
            isSelected={false}
            onClick={vi.fn()}
            onCopy={vi.fn()}
          />
        </Provider>
      );

      expect(screen.getByText('Important Note')).toBeInTheDocument();
    });

    it('should display tags when present', () => {
      const store = createMockStore();
      render(
        <Provider store={store}>
          <ClipboardEntry
            entry={mockEntryWithNameAndTags}
            isSelected={false}
            onClick={vi.fn()}
            onCopy={vi.fn()}
          />
        </Provider>
      );

      expect(screen.getByText('work')).toBeInTheDocument();
      expect(screen.getByText('important')).toBeInTheDocument();
      expect(screen.getByText('meeting')).toBeInTheDocument();
    });

    it('should not display name section when name and tags are empty', () => {
      const store = createMockStore();
      render(
        <Provider store={store}>
          <ClipboardEntry
            entry={mockEntry}
            isSelected={false}
            onClick={vi.fn()}
            onCopy={vi.fn()}
          />
        </Provider>
      );

      expect(screen.queryByText('Important Note')).not.toBeInTheDocument();
      expect(screen.queryByText('work')).not.toBeInTheDocument();
    });
  });

  describe('Edit Button', () => {
    it('should display edit button', () => {
      const store = createMockStore();
      render(
        <Provider store={store}>
          <ClipboardEntry
            entry={mockEntry}
            isSelected={false}
            onClick={vi.fn()}
            onCopy={vi.fn()}
          />
        </Provider>
      );

      const editButton = screen.getByRole('button', { name: /edit name and tags/i });
      expect(editButton).toBeInTheDocument();
    });

    it('should open edit dialog when edit button is clicked', () => {
      const store = createMockStore();
      render(
        <Provider store={store}>
          <ClipboardEntry
            entry={mockEntry}
            isSelected={false}
            onClick={vi.fn()}
            onCopy={vi.fn()}
          />
        </Provider>
      );

      const editButton = screen.getByRole('button', { name: /edit name and tags/i });
      fireEvent.click(editButton);

      expect(screen.getByText('Edit Entry')).toBeInTheDocument();
      expect(screen.getByLabelText('Name')).toBeInTheDocument();
      expect(screen.getByLabelText('Tags')).toBeInTheDocument();
    });

    it('should not trigger entry click when edit button is clicked', () => {
      const store = createMockStore();
      const onClickMock = vi.fn();
      render(
        <Provider store={store}>
          <ClipboardEntry
            entry={mockEntry}
            isSelected={false}
            onClick={onClickMock}
            onCopy={vi.fn()}
          />
        </Provider>
      );

      const editButton = screen.getByRole('button', { name: /edit name and tags/i });
      fireEvent.click(editButton);

      expect(onClickMock).not.toHaveBeenCalled();
    });
  });

  describe('Edit Dialog', () => {
    it('should populate dialog with current name and tags', () => {
      const store = createMockStore();
      render(
        <Provider store={store}>
          <ClipboardEntry
            entry={mockEntryWithNameAndTags}
            isSelected={false}
            onClick={vi.fn()}
            onCopy={vi.fn()}
          />
        </Provider>
      );

      const editButton = screen.getByRole('button', { name: /edit name and tags/i });
      fireEvent.click(editButton);

      const nameInput = screen.getByDisplayValue('Important Note');
      expect(nameInput).toBeInTheDocument();

      // Check that tags are displayed in the autocomplete
      expect(screen.getByText('work')).toBeInTheDocument();
      expect(screen.getByText('important')).toBeInTheDocument();
      expect(screen.getByText('meeting')).toBeInTheDocument();
    });

    it('should show content preview in dialog', () => {
      const store = createMockStore();
      render(
        <Provider store={store}>
          <ClipboardEntry
            entry={mockEntry}
            isSelected={false}
            onClick={vi.fn()}
            onCopy={vi.fn()}
          />
        </Provider>
      );

      const editButton = screen.getByRole('button', { name: /edit name and tags/i });
      fireEvent.click(editButton);

      expect(screen.getByText('Content Preview:')).toBeInTheDocument();
      expect(screen.getByText('Test clipboard content')).toBeInTheDocument();
    });

    it('should close dialog when cancel is clicked', () => {
      const store = createMockStore();
      render(
        <Provider store={store}>
          <ClipboardEntry
            entry={mockEntry}
            isSelected={false}
            onClick={vi.fn()}
            onCopy={vi.fn()}
          />
        </Provider>
      );

      const editButton = screen.getByRole('button', { name: /edit name and tags/i });
      fireEvent.click(editButton);

      const cancelButton = screen.getByRole('button', { name: /cancel/i });
      fireEvent.click(cancelButton);

      expect(screen.queryByText('Edit Entry')).not.toBeInTheDocument();
    });
  });

  describe('Save Functionality', () => {
    it('should call updateEntryName when name is changed', async () => {
      const updatedEntry = { ...mockEntry, name: 'New Name' };
      mockInvoke.mockResolvedValueOnce(updatedEntry);

      const store = createMockStore();
      render(
        <Provider store={store}>
          <ClipboardEntry
            entry={mockEntry}
            isSelected={false}
            onClick={vi.fn()}
            onCopy={vi.fn()}
          />
        </Provider>
      );

      const editButton = screen.getByRole('button', { name: /edit name and tags/i });
      fireEvent.click(editButton);

      const nameInput = screen.getByLabelText('Name');
      fireEvent.change(nameInput, { target: { value: 'New Name' } });

      const saveButton = screen.getByRole('button', { name: /save/i });
      fireEvent.click(saveButton);

      await waitFor(() => {
        expect(mockInvoke).toHaveBeenCalledWith('update_entry_name', {
          entryId: 'test-id-1',
          name: 'New Name',
        });
      });
    });

    it('should call updateEntryTags when tags are changed', async () => {
      const updatedEntry = { ...mockEntry, tags: ['new-tag'] };
      mockInvoke.mockResolvedValueOnce(updatedEntry);

      const store = createMockStore();
      render(
        <Provider store={store}>
          <ClipboardEntry
            entry={mockEntry}
            isSelected={false}
            onClick={vi.fn()}
            onCopy={vi.fn()}
          />
        </Provider>
      );

      const editButton = screen.getByRole('button', { name: /edit name and tags/i });
      fireEvent.click(editButton);

      // Add a new tag (this is simplified - in reality we'd need to interact with the Autocomplete)
      const saveButton = screen.getByRole('button', { name: /save/i });
      fireEvent.click(saveButton);

      await waitFor(() => {
        expect(mockInvoke).toHaveBeenCalledWith('update_entry_tags', {
          entryId: 'test-id-1',
          tags: [],
        });
      });
    });

    it('should not call update functions if values unchanged', async () => {
      const store = createMockStore();
      render(
        <Provider store={store}>
          <ClipboardEntry
            entry={mockEntry}
            isSelected={false}
            onClick={vi.fn()}
            onCopy={vi.fn()}
          />
        </Provider>
      );

      const editButton = screen.getByRole('button', { name: /edit name and tags/i });
      fireEvent.click(editButton);

      const saveButton = screen.getByRole('button', { name: /save/i });
      fireEvent.click(saveButton);

      await waitFor(() => {
        expect(mockInvoke).not.toHaveBeenCalled();
      });
    });

    it('should close dialog after successful save', async () => {
      const updatedEntry = { ...mockEntry, name: 'New Name' };
      mockInvoke.mockResolvedValueOnce(updatedEntry);

      const store = createMockStore();
      render(
        <Provider store={store}>
          <ClipboardEntry
            entry={mockEntry}
            isSelected={false}
            onClick={vi.fn()}
            onCopy={vi.fn()}
          />
        </Provider>
      );

      const editButton = screen.getByRole('button', { name: /edit name and tags/i });
      fireEvent.click(editButton);

      const nameInput = screen.getByLabelText('Name');
      fireEvent.change(nameInput, { target: { value: 'New Name' } });

      const saveButton = screen.getByRole('button', { name: /save/i });
      fireEvent.click(saveButton);

      await waitFor(() => {
        expect(screen.queryByText('Edit Entry')).not.toBeInTheDocument();
      });
    });
  });

  describe('Tag Autocomplete', () => {
    it('should show existing tags from history in autocomplete options', () => {
      const historyWithTags = [
        { ...mockEntry, id: '1', tags: ['work', 'personal'] },
        { ...mockEntry, id: '2', tags: ['meeting', 'important'] },
      ];

      const store = createMockStore({
        clipboard: {
          history: historyWithTags,
          favorites: [],
        },
      });

      render(
        <Provider store={store}>
          <ClipboardEntry
            entry={mockEntry}
            isSelected={false}
            onClick={vi.fn()}
            onCopy={vi.fn()}
          />
        </Provider>
      );

      const editButton = screen.getByRole('button', { name: /edit name and tags/i });
      fireEvent.click(editButton);

      // The autocomplete should have access to existing tags
      // This would need more complex testing to verify the autocomplete options
      expect(screen.getByLabelText('Tags')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('should handle save errors gracefully', async () => {
      mockInvoke.mockRejectedValueOnce(new Error('Save failed'));
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation();

      const store = createMockStore();
      render(
        <Provider store={store}>
          <ClipboardEntry
            entry={mockEntry}
            isSelected={false}
            onClick={vi.fn()}
            onCopy={vi.fn()}
          />
        </Provider>
      );

      const editButton = screen.getByRole('button', { name: /edit name and tags/i });
      fireEvent.click(editButton);

      const nameInput = screen.getByLabelText('Name');
      fireEvent.change(nameInput, { target: { value: 'New Name' } });

      const saveButton = screen.getByRole('button', { name: /save/i });
      fireEvent.click(saveButton);

      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalledWith('Failed to update entry:', expect.any(Error));
      });

      consoleSpy.mockRestore();
    });
  });

  describe('Accessibility', () => {
    it('should have proper test ids for testing', () => {
      const store = createMockStore();
      render(
        <Provider store={store}>
          <ClipboardEntry
            entry={mockEntry}
            isSelected={false}
            onClick={vi.fn()}
            onCopy={vi.fn()}
          />
        </Provider>
      );

      const editButton = screen.getByRole('button', { name: /edit name and tags/i });
      fireEvent.click(editButton);

      expect(screen.getByTestId('edit-name-input')).toBeInTheDocument();
      expect(screen.getByTestId('edit-tags-input')).toBeInTheDocument();
      expect(screen.getByTestId('edit-tags-autocomplete')).toBeInTheDocument();
    });

    it('should have proper ARIA labels', () => {
      const store = createMockStore();
      render(
        <Provider store={store}>
          <ClipboardEntry
            entry={mockEntry}
            isSelected={false}
            onClick={vi.fn()}
            onCopy={vi.fn()}
          />
        </Provider>
      );

      const editButton = screen.getByRole('button', { name: /edit name and tags/i });
      expect(editButton).toHaveAttribute('aria-label', expect.stringContaining('Edit'));
    });
  });
});
