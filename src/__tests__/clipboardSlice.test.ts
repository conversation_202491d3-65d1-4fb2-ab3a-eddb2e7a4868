import { configureStore } from '@reduxjs/toolkit';
import clipboardSlice, {
  setSearchQuery,
  setSelectedEntry,
  addEntry,
  updateMaxHistoryLength,
  getClipboardHistory,
  saveClipboardEntry,
  toggleFavorite,
  clearHistory,
  deleteEntry,
  startMonitoring,
  stopMonitoring,
  checkMonitoringStatus,
} from '@/store/slices/clipboardSlice';
import type { ClipboardEntry } from '@/types/clipboard';

const mockEntries: ClipboardEntry[] = [
  {
    id: '1',
    content: 'First entry',
    type: 'text',
    timestamp: Date.now() - 1000,
    isFavorite: false,
    tags: [],
  },
  {
    id: '2',
    content: 'Second entry',
    type: 'text',
    timestamp: Date.now() - 2000,
    isFavorite: true,
    tags: [],
  },
  {
    id: '3',
    content: 'Third entry',
    type: 'text',
    timestamp: Date.now() - 3000,
    isFavorite: false,
    tags: [],
  },
];

const createStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      clipboard: clipboardSlice,
    },
    preloadedState: {
      clipboard: {
        history: [],
        favorites: [],
        maxHistoryLength: 100,
        isMonitoring: false,
        searchQuery: '',
        selectedEntryId: null,
        selectedTags: [],
        ...initialState,
      },
    },
  });
};

describe('clipboardSlice', () => {
  beforeEach(() => {
    globalThis.mockTauriInvoke.mockClear();
  });

  describe('reducers', () => {
    it('should handle setSearchQuery', () => {
      const store = createStore();
      
      store.dispatch(setSearchQuery('test query'));
      
      expect(store.getState().clipboard.searchQuery).toBe('test query');
    });

    it('should handle setSelectedEntry', () => {
      const store = createStore();
      
      store.dispatch(setSelectedEntry('entry-1'));
      
      expect(store.getState().clipboard.selectedEntryId).toBe('entry-1');
    });

    it('should handle addEntry', () => {
      const store = createStore();
      const newEntry: ClipboardEntry = {
        id: 'new-1',
        content: 'New entry',
        type: 'text',
        timestamp: Date.now(),
        isFavorite: false,
        tags: [],
      };
      
      store.dispatch(addEntry(newEntry));
      
      const state = store.getState().clipboard;
      expect(state.history).toHaveLength(1);
      expect(state.history[0]).toEqual(newEntry);
    });

    it('should add entry to beginning of history', () => {
      const store = createStore({ history: mockEntries });
      const newEntry: ClipboardEntry = {
        id: 'new-1',
        content: 'New entry',
        type: 'text',
        timestamp: Date.now(),
        isFavorite: false,
        tags: [],
      };
      
      store.dispatch(addEntry(newEntry));
      
      const state = store.getState().clipboard;
      expect(state.history[0]).toEqual(newEntry);
      expect(state.history).toHaveLength(4);
    });

    it('should trim history when exceeding max length', () => {
      const store = createStore({ 
        history: mockEntries,
        maxHistoryLength: 3,
      });
      const newEntry: ClipboardEntry = {
        id: 'new-1',
        content: 'New entry',
        type: 'text',
        timestamp: Date.now(),
        isFavorite: false,
        tags: [],
      };
      
      store.dispatch(addEntry(newEntry));
      
      const state = store.getState().clipboard;
      expect(state.history).toHaveLength(3);
      expect(state.history[0]).toEqual(newEntry);
    });

    it('should handle updateMaxHistoryLength', () => {
      const store = createStore({ 
        history: mockEntries,
        maxHistoryLength: 100,
      });
      
      store.dispatch(updateMaxHistoryLength(2));
      
      const state = store.getState().clipboard;
      expect(state.maxHistoryLength).toBe(2);
      expect(state.history).toHaveLength(2); // Should trim existing history
    });
  });

  describe('async thunks', () => {
    describe('getClipboardHistory', () => {
      it('should fetch clipboard history successfully', async () => {
        globalThis.mockTauriInvoke.mockResolvedValueOnce(mockEntries);
        const store = createStore();
        
        await store.dispatch(getClipboardHistory());
        
        const state = store.getState().clipboard;
        expect(state.history).toEqual(mockEntries);
        expect(state.favorites).toEqual([mockEntries[1]]); // Only favorite entry
        expect(globalThis.mockTauriInvoke).toHaveBeenCalledWith('get_clipboard_history');
      });

      it('should handle fetch error', async () => {
        globalThis.mockTauriInvoke.mockRejectedValueOnce(new Error('Fetch failed'));
        const store = createStore();
        
        const result = await store.dispatch(getClipboardHistory());
        
        expect(result.type).toBe('clipboard/getHistory/rejected');
      });
    });

    describe('saveClipboardEntry', () => {
      it('should save clipboard entry successfully', async () => {
        const newEntry: ClipboardEntry = {
          id: 'saved-1',
          content: 'Saved entry',
          type: 'text',
          timestamp: Date.now(),
          isFavorite: false,
          tags: [],
        };
        globalThis.mockTauriInvoke.mockResolvedValueOnce(newEntry);
        const store = createStore();
        
        await store.dispatch(saveClipboardEntry({
          content: 'Saved entry',
          type: 'text',
          isFavorite: false,
          tags: [],
        }));
        
        const state = store.getState().clipboard;
        expect(state.history[0]).toEqual(newEntry);
        expect(globalThis.mockTauriInvoke).toHaveBeenCalledWith('save_clipboard_entry', {
          entry: {
            content: 'Saved entry',
            type: 'text',
            isFavorite: false,
            tags: [],
          },
        });
      });

      it('should trim history after saving when exceeding max length', async () => {
        const newEntry: ClipboardEntry = {
          id: 'saved-1',
          content: 'Saved entry',
          type: 'text',
          timestamp: Date.now(),
          isFavorite: false,
          tags: [],
        };
        globalThis.mockTauriInvoke.mockResolvedValueOnce(newEntry);
        const store = createStore({ 
          history: mockEntries,
          maxHistoryLength: 3,
        });
        
        await store.dispatch(saveClipboardEntry({
          content: 'Saved entry',
          type: 'text',
          isFavorite: false,
          tags: [],
        }));
        
        const state = store.getState().clipboard;
        expect(state.history).toHaveLength(3);
        expect(state.history[0]).toEqual(newEntry);
      });
    });

    describe('toggleFavorite', () => {
      it('should toggle favorite status successfully', async () => {
        const updatedEntry: ClipboardEntry = {
          ...mockEntries[0],
          isFavorite: true,
        };
        globalThis.mockTauriInvoke.mockResolvedValueOnce(updatedEntry);
        const store = createStore({ history: mockEntries });
        
        await store.dispatch(toggleFavorite('1'));
        
        const state = store.getState().clipboard;
        expect(state.history[0].isFavorite).toBe(true);
        expect(state.favorites).toContainEqual(updatedEntry);
        expect(globalThis.mockTauriInvoke).toHaveBeenCalledWith('toggle_favorite', { entryId: '1' });
      });

      it('should remove from favorites when toggling off', async () => {
        const updatedEntry: ClipboardEntry = {
          ...mockEntries[1],
          isFavorite: false,
        };
        globalThis.mockTauriInvoke.mockResolvedValueOnce(updatedEntry);
        const store = createStore({ 
          history: mockEntries,
          favorites: [mockEntries[1]],
        });
        
        await store.dispatch(toggleFavorite('2'));
        
        const state = store.getState().clipboard;
        expect(state.history[1].isFavorite).toBe(false);
        expect(state.favorites).not.toContainEqual(updatedEntry);
      });

      it('should update existing favorite entry', async () => {
        const updatedEntry: ClipboardEntry = {
          ...mockEntries[1],
          content: 'Updated content',
          isFavorite: true,
        };
        globalThis.mockTauriInvoke.mockResolvedValueOnce(updatedEntry);
        const store = createStore({ 
          history: mockEntries,
          favorites: [mockEntries[1]],
        });
        
        await store.dispatch(toggleFavorite('2'));
        
        const state = store.getState().clipboard;
        expect(state.favorites[0].content).toBe('Updated content');
      });
    });

    describe('clearHistory', () => {
      it('should clear history successfully', async () => {
        globalThis.mockTauriInvoke.mockResolvedValueOnce(undefined);
        const store = createStore({ 
          history: mockEntries,
          selectedEntryId: '1',
        });
        
        await store.dispatch(clearHistory());
        
        const state = store.getState().clipboard;
        expect(state.history).toEqual([]);
        expect(state.selectedEntryId).toBeNull();
        expect(globalThis.mockTauriInvoke).toHaveBeenCalledWith('clear_clipboard_history');
      });
    });

    describe('deleteEntry', () => {
      it('should delete entry successfully', async () => {
        globalThis.mockTauriInvoke.mockResolvedValueOnce(undefined);
        const store = createStore({ 
          history: mockEntries,
          favorites: [mockEntries[1]],
          selectedEntryId: '2',
        });
        
        await store.dispatch(deleteEntry('2'));
        
        const state = store.getState().clipboard;
        expect(state.history).not.toContainEqual(mockEntries[1]);
        expect(state.favorites).toEqual([]);
        expect(state.selectedEntryId).toBeNull();
        expect(globalThis.mockTauriInvoke).toHaveBeenCalledWith('delete_clipboard_entry', { entryId: '2' });
      });

      it('should not change selectedEntryId if different entry is deleted', async () => {
        globalThis.mockTauriInvoke.mockResolvedValueOnce(undefined);
        const store = createStore({ 
          history: mockEntries,
          selectedEntryId: '1',
        });
        
        await store.dispatch(deleteEntry('2'));
        
        const state = store.getState().clipboard;
        expect(state.selectedEntryId).toBe('1');
      });
    });

    describe('startMonitoring', () => {
      it('should start monitoring successfully', async () => {
        globalThis.mockTauriInvoke.mockResolvedValueOnce(undefined);
        const store = createStore();
        
        await store.dispatch(startMonitoring());
        
        const state = store.getState().clipboard;
        expect(state.isMonitoring).toBe(true);
        expect(globalThis.mockTauriInvoke).toHaveBeenCalledWith('start_clipboard_monitoring');
      });
    });

    describe('stopMonitoring', () => {
      it('should stop monitoring successfully', async () => {
        globalThis.mockTauriInvoke.mockResolvedValueOnce(undefined);
        const store = createStore({ isMonitoring: true });
        
        await store.dispatch(stopMonitoring());
        
        const state = store.getState().clipboard;
        expect(state.isMonitoring).toBe(false);
        expect(globalThis.mockTauriInvoke).toHaveBeenCalledWith('stop_clipboard_monitoring');
      });
    });

    describe('checkMonitoringStatus', () => {
      it('should check monitoring status successfully', async () => {
        globalThis.mockTauriInvoke.mockResolvedValueOnce(true);
        const store = createStore();
        
        await store.dispatch(checkMonitoringStatus());
        
        const state = store.getState().clipboard;
        expect(state.isMonitoring).toBe(true);
        expect(globalThis.mockTauriInvoke).toHaveBeenCalledWith('is_monitoring_clipboard');
      });
    });
  });

  describe('initial state', () => {
    it('should have correct initial state', () => {
      const store = createStore();
      const state = store.getState().clipboard;
      
      expect(state).toEqual({
        history: [],
        favorites: [],
        maxHistoryLength: 100,
        isMonitoring: false,
        searchQuery: '',
        selectedEntryId: null,
        selectedTags: [],
      });
    });
  });

  describe('edge cases', () => {
    it('should handle empty history when trimming', () => {
      const store = createStore({ history: [] });
      
      store.dispatch(updateMaxHistoryLength(50));
      
      const state = store.getState().clipboard;
      expect(state.history).toEqual([]);
      expect(state.maxHistoryLength).toBe(50);
    });

    it('should handle toggle favorite for non-existent entry', async () => {
      const updatedEntry: ClipboardEntry = {
        id: 'non-existent',
        content: 'New entry',
        type: 'text',
        timestamp: Date.now(),
        isFavorite: true,
        tags: [],
      };
      globalThis.mockTauriInvoke.mockResolvedValueOnce(updatedEntry);
      const store = createStore({ history: mockEntries });
      
      await store.dispatch(toggleFavorite('non-existent'));
      
      const state = store.getState().clipboard;
      // Should add to favorites even if not in history
      expect(state.favorites).toContainEqual(updatedEntry);
    });

    it('should handle adding entry with zero max history length', () => {
      const store = createStore({ maxHistoryLength: 0 });
      const newEntry: ClipboardEntry = {
        id: 'new-1',
        content: 'New entry',
        type: 'text',
        timestamp: Date.now(),
        isFavorite: false,
        tags: [],
      };
      
      store.dispatch(addEntry(newEntry));
      
      const state = store.getState().clipboard;
      expect(state.history).toEqual([]);
    });
  });
});
