mod clipboard;
mod clipboard_monitor;

use clipboard::{ClipboardManager, ClipboardEntry, Preferences};
use clipboard_monitor::ClipboardMonitor;
use std::sync::{Arc, Mutex};
use tauri::{State, Manager, AppHandle};
use tauri::tray::{TrayIconBuilder, TrayIconEvent, MouseButton, MouseButtonState};
use tauri::menu::{Menu, MenuItem};


// Application state
pub struct AppState {
    clipboard_manager: Arc<ClipboardManager>,
    clipboard_monitor: Arc<ClipboardMonitor>,
    system_tray_enabled: Arc<Mutex<bool>>,
    app_handle: Arc<Mutex<Option<AppHandle>>>,
}

// Tauri commands
#[tauri::command]
async fn get_clipboard_history(state: State<'_, AppState>) -> Result<Vec<ClipboardEntry>, String> {
    state.clipboard_manager.get_history()
}

#[tauri::command]
async fn save_clipboard_entry(
    entry: serde_json::Value,
    state: State<'_, AppState>,
) -> Result<ClipboardEntry, String> {
    let content = entry["content"].as_str().unwrap_or("").to_string();
    let entry_type = entry["type"].as_str().unwrap_or("text").to_string();
    state.clipboard_manager.add_entry(content, entry_type)
}

#[tauri::command]
async fn toggle_favorite(
    entry_id: String,
    state: State<'_, AppState>,
) -> Result<ClipboardEntry, String> {
    state.clipboard_manager.toggle_favorite(&entry_id)
}

#[tauri::command]
async fn delete_clipboard_entry(
    entry_id: String,
    state: State<'_, AppState>,
) -> Result<(), String> {
    state.clipboard_manager.delete_entry(&entry_id)
}

#[tauri::command]
async fn clear_clipboard_history(state: State<'_, AppState>) -> Result<(), String> {
    state.clipboard_manager.clear_history()
}

#[tauri::command]
async fn get_preferences(state: State<'_, AppState>) -> Result<Preferences, String> {
    state.clipboard_manager.get_preferences()
}

#[tauri::command]
async fn save_preferences(
    preferences: Preferences,
    state: State<'_, AppState>,
) -> Result<Preferences, String> {
    state.clipboard_manager.update_preferences(preferences)
}

#[tauri::command]
async fn start_clipboard_monitoring(state: State<'_, AppState>) -> Result<(), String> {
    state.clipboard_monitor.start_monitoring().await.map_err(|e| e.to_string())
}

#[tauri::command]
async fn stop_clipboard_monitoring(state: State<'_, AppState>) -> Result<(), String> {
    state.clipboard_monitor.stop_monitoring().map_err(|e| e.to_string())
}

#[tauri::command]
async fn is_monitoring_clipboard(state: State<'_, AppState>) -> Result<bool, String> {
    Ok(state.clipboard_monitor.is_running())
}

#[tauri::command]
async fn update_global_shortcuts(shortcuts: serde_json::Value) -> Result<(), String> {
    // TODO: Implement global shortcut updates with the provided shortcuts
    println!("Updating global shortcuts: {:?}", shortcuts);
    Ok(())
}

#[tauri::command]
async fn set_auto_start(enabled: bool) -> Result<(), String> {
    use auto_launch::AutoLaunch;

    let app_name = "Paste King";
    let app_path = std::env::current_exe().map_err(|e| {
        eprintln!("Failed to get current executable path: {}", e);
        format!("Failed to get executable path: {}", e)
    })?;

    println!("Setting auto-start to: {} for app: {} at path: {:?}", enabled, app_name, app_path);

    let auto = AutoLaunch::new(
        app_name,
        &app_path.to_string_lossy(),
        false, // use_launch_agent parameter for macOS
        &[] as &[&str],
    );

    let result = if enabled {
        auto.enable().map_err(|e| {
            eprintln!("Failed to enable auto-start: {}", e);
            format!("Failed to enable auto-start: {}", e)
        })
    } else {
        auto.disable().map_err(|e| {
            eprintln!("Failed to disable auto-start: {}", e);
            format!("Failed to disable auto-start: {}", e)
        })
    };

    match result {
        Ok(_) => {
            println!("Auto-start {} successfully", if enabled { "enabled" } else { "disabled" });
            Ok(())
        }
        Err(e) => Err(e),
    }
}

#[tauri::command]
async fn set_system_tray(enabled: bool, state: State<'_, AppState>) -> Result<(), String> {
    println!("System tray setting changed to: {}", enabled);

    // Update the state
    {
        let mut tray_enabled = state.system_tray_enabled.lock().map_err(|e| e.to_string())?;
        *tray_enabled = enabled;
    }

    // Get the app handle and manage system tray visibility
    let app_handle = {
        let handle_guard = state.app_handle.lock().map_err(|e| e.to_string())?;
        handle_guard.clone()
    };

    if let Some(_app_handle) = app_handle {
        // In Tauri v2, we need to recreate the tray or manage it differently
        // For now, we'll just log the setting change and store it in preferences
        // The tray visibility will be managed during app setup based on preferences
        println!("System tray setting updated to: {}", enabled);

        // Note: In Tauri v2, tray icons are managed differently
        // The visibility will be handled during app initialization
        // based on the saved preferences
    } else {
        eprintln!("App handle not available");
        return Err("App handle not available".to_string());
    }

    Ok(())
}

#[tauri::command]
async fn get_system_tray_status(state: State<'_, AppState>) -> Result<bool, String> {
    let tray_enabled = state.system_tray_enabled.lock().map_err(|e| e.to_string())?;
    Ok(*tray_enabled)
}

#[tauri::command]
async fn update_entry_name(
    entry_id: String,
    name: String,
    state: State<'_, AppState>,
) -> Result<ClipboardEntry, String> {
    state.clipboard_manager.update_entry_name(&entry_id, name)
}

#[tauri::command]
async fn update_entry_tags(
    entry_id: String,
    tags: Vec<String>,
    state: State<'_, AppState>,
) -> Result<ClipboardEntry, String> {
    state.clipboard_manager.update_entry_tags(&entry_id, tags)
}

#[tauri::command]
async fn get_clipboard_status(state: State<'_, AppState>) -> Result<String, String> {
    let is_monitoring = state.clipboard_monitor.is_running();
    let history = state.clipboard_manager.get_history()?;
    Ok(format!("Monitoring: {}, History entries: {}", is_monitoring, history.len()))
}

// Helper function to create system tray
fn create_system_tray(app: &AppHandle) -> Result<(), Box<dyn std::error::Error>> {
    let show_hide = MenuItem::with_id(app, "show_hide", "Show/Hide", true, None::<&str>)?;
    let quit = MenuItem::with_id(app, "quit", "Quit", true, None::<&str>)?;

    let menu = Menu::with_items(app, &[&show_hide, &quit])?;

    println!("Creating tray icon using default window icon");

    // Use the default window icon instead of trying to load from bytes
    let icon = app.default_window_icon()
        .ok_or("No default window icon available")?
        .clone();

    println!("Icon loaded successfully, creating tray...");

    let _tray = TrayIconBuilder::new()
        .icon(icon)
        .menu(&menu)
        .tooltip("Paste King - Clipboard Manager")
        .on_menu_event(|app, event| {
            match event.id.as_ref() {
                "show_hide" => {
                    if let Some(window) = app.get_webview_window("main") {
                        if window.is_visible().unwrap_or(false) {
                            let _ = window.hide();
                        } else {
                            let _ = window.show();
                            let _ = window.set_focus();
                        }
                    }
                }
                "quit" => {
                    app.exit(0);
                }
                _ => {}
            }
        })
        .on_tray_icon_event(|tray, event| {
            match event {
                TrayIconEvent::Click {
                    button: MouseButton::Left,
                    button_state: MouseButtonState::Up,
                    ..
                } => {
                    let app = tray.app_handle();
                    if let Some(window) = app.get_webview_window("main") {
                        if window.is_visible().unwrap_or(false) {
                            let _ = window.hide();
                        } else {
                            let _ = window.show();
                            let _ = window.set_focus();
                        }
                    }
                }
                TrayIconEvent::DoubleClick {
                    button: MouseButton::Left,
                    ..
                } => {
                    let app = tray.app_handle();
                    if let Some(window) = app.get_webview_window("main") {
                        let _ = window.show();
                        let _ = window.set_focus();
                    }
                }
                _ => {}
            }
        })
        .build(app)?;

    println!("Tray icon built successfully");
    Ok(())
}



#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    let clipboard_manager = Arc::new(ClipboardManager::new());
    let clipboard_monitor = Arc::new(
        ClipboardMonitor::new(clipboard_manager.clone())
            .expect("Failed to initialize clipboard monitor")
    );

    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_clipboard_manager::init())
        .plugin(tauri_plugin_global_shortcut::Builder::new().build())
        .plugin(tauri_plugin_store::Builder::default().build())


        .manage(AppState {
            clipboard_manager: clipboard_manager.clone(),
            clipboard_monitor: clipboard_monitor.clone(),
            system_tray_enabled: Arc::new(Mutex::new(true)), // Default to enabled
            app_handle: Arc::new(Mutex::new(None)),
        })
        .invoke_handler(tauri::generate_handler![
            get_clipboard_history,
            save_clipboard_entry,
            toggle_favorite,
            delete_clipboard_entry,
            clear_clipboard_history,
            get_preferences,
            save_preferences,
            start_clipboard_monitoring,
            stop_clipboard_monitoring,
            is_monitoring_clipboard,
            get_clipboard_status,
            update_global_shortcuts,
            set_auto_start,
            set_system_tray,
            get_system_tray_status,
            update_entry_name,
            update_entry_tags,
        ])
        .setup(move |app| {
            // Store the app handle in the state for system tray management
            let app_handle = app.handle().clone();
            let state = app.state::<AppState>();
            {
                let mut handle_guard = state.app_handle.lock().unwrap();
                *handle_guard = Some(app_handle.clone());
            }

            let monitor_clone = clipboard_monitor.clone();
            let clipboard_manager_clone = clipboard_manager.clone();

            // Load preferences and configure system tray
            let app_handle_clone = app_handle.clone();
            tauri::async_runtime::spawn(async move {
                if let Ok(preferences) = clipboard_manager_clone.get_preferences() {
                    println!("System tray enabled: {}", preferences.show_in_system_tray);

                    // Create system tray if enabled
                    if preferences.show_in_system_tray {
                        if let Err(e) = create_system_tray(&app_handle_clone) {
                            eprintln!("Failed to create system tray: {}", e);
                        } else {
                            println!("System tray created successfully");
                        }
                    }
                }
            });

            // Start clipboard monitoring automatically
            tauri::async_runtime::spawn(async move {
                if let Err(e) = monitor_clone.start_monitoring().await {
                    eprintln!("Failed to start clipboard monitoring: {}", e);
                } else {
                    println!("Clipboard monitoring started successfully");
                }
            });

            Ok(())
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
